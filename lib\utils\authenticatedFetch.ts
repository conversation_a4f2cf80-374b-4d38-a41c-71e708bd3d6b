import { supabase } from '@/lib/supabaseClient';

/**
 * Utility function to make authenticated API calls with automatic token inclusion
 */
export async function authenticatedFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  // Get the current session token
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session?.access_token) {
    throw new Error('No authentication token found. Please log in again.');
  }

  // Merge headers with authorization
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${session.access_token}`,
    ...options.headers,
  };

  // Make the authenticated request
  const response = await fetch(url, {
    ...options,
    headers,
  });

  return response;
}

/**
 * Utility function to make authenticated API calls and parse JSON response
 */
export async function authenticatedFetchJson<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await authenticatedFetch(url, options);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}
