"use client";

import React from "react";
import { useSelector } from "react-redux";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, Plus } from "lucide-react";

// Hooks
import { useLeadSourcesData } from "./hooks/useLeadSourcesData";
import { useLeadSourceActions } from "./hooks/useLeadSourceActions";
import { useLeadSourceDialog } from "./hooks/useLeadSourceDialog";

// Components
import { LeadSourcesSkeleton } from "./components/LeadSourcesSkeleton";
import { LeadSourcesTable } from "./components/LeadSourcesTable";
import { LeadSourceFormDialog, DeleteConfirmationDialog } from "./components/LeadSourceDialogs";
import { EmptyState } from "./components/EmptyState";

// Types
import { RootState } from "@/lib/store/store";
import { LeadSourceFormData } from "./schemas/leadSourceSchema";

const LeadSourcesPage: React.FC = () => {
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);

  // Fetch lead sources data
  const {
    workspaceData,
    sources,
    setSources,
    isInitialLoading,
    hasWorkspace,
  } = useLeadSourcesData();

  // Lead source actions
  const {
    createLeadSource,
    updateLeadSource,
    deleteLeadSource,
    toggleWebhookStatus,
    copyWebhookUrl,
    isCreating,
    isUpdating,
    isDeleting,
    isStatusChanging,
  } = useLeadSourceActions({
    sources,
    setSources,
    workspaceData,
  });

  // Dialog management
  const {
    dialogMode,
    selectedSource,
    expandedRow,
    form,
    resetDialog,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    toggleRow,
    isDialogOpen,
  } = useLeadSourceDialog({ workspaceData });

  // Handle form submission
  const handleFormSubmit = async (data: LeadSourceFormData) => {
    let success = false;

    if (dialogMode === "create") {
      success = await createLeadSource(data);
    } else if (dialogMode === "edit" && selectedSource) {
      success = await updateLeadSource(selectedSource.id, data);
    }

    if (success) {
      resetDialog();
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (selectedSource) {
      const success = await deleteLeadSource(selectedSource.id);
      if (success) {
        resetDialog();
      }
    }
  };

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return <LeadSourcesSkeleton isCollapsed={isCollapsed} />;
  }

  // Show empty state if no workspace or no sources
  if (!hasWorkspace || sources.length === 0) {
    return (
      <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
        <EmptyState onCreateSource={openCreateDialog} hasWorkspace={hasWorkspace} />
      </div>
    );
  }

  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        {/* Header */}
        <CardHeader className="flex flex-row justify-between items-center bg-white dark:bg-black">
          <div className="flex gap-6 items-center">
            <Users className="md:hidden lg:hidden h-4 w-4 text-gray-600 dark:text-gray-400" />
            <CardTitle className="text-lg md:text-2xl font-bold text-gray-900 dark:text-white">
              Lead Sources
            </CardTitle>
          </div>
          <Button
            onClick={openCreateDialog}
            className="bg-black hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-200 text-white dark:text-black"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Source
          </Button>
        </CardHeader>

        {/* Table */}
        <CardContent>
          <LeadSourcesTable
            sources={sources}
            expandedRow={expandedRow}
            onToggleRow={toggleRow}
            onEditSource={openEditDialog}
            onDeleteSource={openDeleteDialog}
            onToggleStatus={toggleWebhookStatus}
            onCopyWebhook={copyWebhookUrl}
            isStatusChanging={isStatusChanging}
          />
        </CardContent>
      </Card>

      {/* Form Dialog */}
      <LeadSourceFormDialog
        isOpen={isDialogOpen("create") || isDialogOpen("edit")}
        mode={dialogMode === "create" ? "create" : "edit"}
        form={form}
        isLoading={isCreating || isUpdating}
        onClose={resetDialog}
        onSubmit={handleFormSubmit}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={isDialogOpen("delete")}
        source={selectedSource}
        isLoading={isDeleting}
        onClose={resetDialog}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
};

export default LeadSourcesPage;