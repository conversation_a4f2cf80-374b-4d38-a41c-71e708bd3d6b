import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { authenticatedFetchJson } from '@/lib/utils/authenticatedFetch';

interface Workspace {
  id: string;
  name: string;
  role: string;
  isActive: boolean;
  isOwner: boolean;
  companyType?: string;
  membershipId: string;
}

interface WorkspaceData {
  workspaces: Workspace[];
  activeWorkspace?: Workspace;
  totalCount: number;
}

/**
 * Optimized hook for workspace management with caching and efficient switching
 */
export function useOptimizedWorkspaces() {
  const queryClient = useQueryClient();
  const [isSwitching, setIsSwitching] = useState(false);

  // Fetch user's workspaces with caching
  const {
    data: workspaceData,
    isLoading,
    error,
    refetch
  } = useQuery<WorkspaceData>({
    queryKey: ['user-workspaces'],
    queryFn: async () => {
      const result = await authenticatedFetch<PERSON><PERSON>('/api/workspace/switch', {
        method: 'GET',
      });
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });

  // Switch workspace mutation
  const switchWorkspaceMutation = useMutation({
    mutationFn: async (workspaceId: string) => {
      return authenticatedFetchJson('/api/workspace/switch', {
        method: 'POST',
        body: JSON.stringify({ workspaceId }),
      });
    },
    onMutate: async (workspaceId: string) => {
      setIsSwitching(true);

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['user-workspaces'] });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData<WorkspaceData>(['user-workspaces']);

      // Optimistically update the cache
      if (previousData) {
        const updatedWorkspaces = previousData.workspaces.map(workspace => ({
          ...workspace,
          isActive: workspace.id === workspaceId
        }));

        const newActiveWorkspace = updatedWorkspaces.find(w => w.id === workspaceId);

        queryClient.setQueryData<WorkspaceData>(['user-workspaces'], {
          ...previousData,
          workspaces: updatedWorkspaces,
          activeWorkspace: newActiveWorkspace
        });
      }

      return { previousData };
    },
    onError: (error, workspaceId, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(['user-workspaces'], context.previousData);
      }

      console.error('Error switching workspace:', error);
      toast.error(error.message || 'Failed to switch workspace');
    },
    onSuccess: (data, workspaceId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['active-workspace'] });
      queryClient.invalidateQueries({ queryKey: ['workspace-data', workspaceId] });
      queryClient.invalidateQueries({ queryKey: ['workspace-members', workspaceId] });

      const workspace = workspaceData?.workspaces.find(w => w.id === workspaceId);
      toast.success(`Switched to ${workspace?.name || 'workspace'}`);
    },
    onSettled: () => {
      setIsSwitching(false);
    },
  });

  // Switch workspace function
  const switchWorkspace = useCallback(async (workspaceId: string) => {
    if (isSwitching) return;

    const currentActive = workspaceData?.activeWorkspace;
    if (currentActive?.id === workspaceId) {
      toast.info('Already on this workspace');
      return;
    }

    try {
      await switchWorkspaceMutation.mutateAsync(workspaceId);
    } catch (error) {
      // Error handling is done in the mutation
    }
  }, [workspaceData?.activeWorkspace, isSwitching, switchWorkspaceMutation]);

  // Get workspace by ID
  const getWorkspaceById = useCallback((workspaceId: string) => {
    return workspaceData?.workspaces.find(w => w.id === workspaceId);
  }, [workspaceData?.workspaces]);

  // Check if user is admin/owner of a workspace
  const isWorkspaceAdmin = useCallback((workspaceId: string) => {
    const workspace = getWorkspaceById(workspaceId);
    return workspace?.isOwner || workspace?.role === 'admin' || workspace?.role === 'SuperAdmin';
  }, [getWorkspaceById]);

  // Refresh workspaces
  const refreshWorkspaces = useCallback(async () => {
    await refetch();
  }, [refetch]);

  // Add new workspace to cache (for when user creates/joins a new workspace)
  const addWorkspaceToCache = useCallback((newWorkspace: Workspace) => {
    queryClient.setQueryData<WorkspaceData>(['user-workspaces'], (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        workspaces: [...oldData.workspaces, newWorkspace],
        totalCount: oldData.totalCount + 1,
        activeWorkspace: newWorkspace.isActive ? newWorkspace : oldData.activeWorkspace
      };
    });
  }, [queryClient]);

  // Remove workspace from cache (for when user leaves a workspace)
  const removeWorkspaceFromCache = useCallback((workspaceId: string) => {
    queryClient.setQueryData<WorkspaceData>(['user-workspaces'], (oldData) => {
      if (!oldData) return oldData;

      const filteredWorkspaces = oldData.workspaces.filter(w => w.id !== workspaceId);
      const wasActive = oldData.activeWorkspace?.id === workspaceId;

      return {
        ...oldData,
        workspaces: filteredWorkspaces,
        totalCount: oldData.totalCount - 1,
        activeWorkspace: wasActive ? filteredWorkspaces[0] : oldData.activeWorkspace
      };
    });
  }, [queryClient]);

  return {
    // Data
    workspaces: workspaceData?.workspaces || [],
    activeWorkspace: workspaceData?.activeWorkspace,
    totalCount: workspaceData?.totalCount || 0,

    // Loading states
    isLoading,
    isSwitching,
    error,

    // Actions
    switchWorkspace,
    refreshWorkspaces,
    getWorkspaceById,
    isWorkspaceAdmin,
    addWorkspaceToCache,
    removeWorkspaceFromCache,

    // Mutation state
    switchError: switchWorkspaceMutation.error,
    isError: !!error || switchWorkspaceMutation.isError,
  };
}
