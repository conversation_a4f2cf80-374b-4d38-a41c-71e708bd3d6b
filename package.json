{"name": "infilabs-crm", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "version:patch": "node scripts/version.js patch", "version:minor": "node scripts/version.js minor", "version:major": "node scripts/version.js major", "release": "npm run build && node scripts/version.js"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@lottiefiles/react-lottie-player": "^3.5.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@reduxjs/toolkit": "^2.2.1", "@stripe/stripe-js": "^6.1.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.8", "axios": "^1.9.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cors": "^2.8.5", "critters": "^0.0.23", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.5.1", "framer-motion": "^12.11.2", "input-otp": "^1.4.1", "install": "^0.13.0", "lottie-react": "^2.4.0", "lucide-react": "^0.446.0", "next": "14.1.0", "next-themes": "^0.3.0", "node-fetch": "^2.7.0", "nodemailer": "^6.9.16", "npm": "^10.9.2", "openai": "^4.77.0", "papaparse": "^5.5.2", "pg": "^8.16.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^9.4.4", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-redux": "^9.1.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.7", "recharts": "^2.12.0", "redis": "^4.7.0", "socket.io": "^4.8.1", "sonner": "^1.7.2", "stripe": "^17.7.0", "supabase": "^2.24.3", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "together-ai": "^0.10.0", "typescript": "^5.3.3", "uuid": "^11.0.3", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/jest": "^29.5.14", "@types/node": "^20.17.10", "@types/nodemailer": "^6.4.17", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.19", "@types/react-resizable": "^3.0.8", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "postcss": "^8.4.35"}}