-- Fix workspace switching error P0001
-- This migration addresses the "User can only have one active workspace at a time" error

-- 1. Drop the existing trigger and function
DROP TRIGGER IF EXISTS ensure_single_active_workspace_trigger ON workspace_members;
DROP FUNCTION IF EXISTS ensure_single_active_workspace();

-- 2. <PERSON><PERSON> improved function without the problematic constraint check
CREATE OR REPLACE FUNCTION ensure_single_active_workspace()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting a workspace as active, deactivate all others for this user
  IF NEW.is_active = true AND (OLD.is_active IS NULL OR OLD.is_active = false) THEN
    -- Deactivate all other workspaces for this user in a single atomic operation
    UPDATE workspace_members
    SET is_active = false, updated_at = NOW()
    WHERE user_id = NEW.user_id
    AND id != NEW.id
    AND is_active = true;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. Recreate the trigger
CREATE TRIGGER ensure_single_active_workspace_trigger
  BEFORE UPDATE ON workspace_members
  FOR EACH ROW
  EXECUTE FUNCTION ensure_single_active_workspace();

-- 4. Clean up any existing data inconsistencies
DO $$
DECLARE
  user_record RECORD;
  active_count INTEGER;
  first_membership_id INTEGER;
BEGIN
  -- Find users with multiple active workspaces and fix them
  FOR user_record IN
    SELECT user_id, COUNT(*) as active_count
    FROM workspace_members
    WHERE is_active = true
    GROUP BY user_id
    HAVING COUNT(*) > 1
  LOOP
    -- Get the most recently updated membership for this user
    SELECT id INTO first_membership_id
    FROM workspace_members
    WHERE user_id = user_record.user_id
    AND is_active = true
    ORDER BY updated_at DESC, created_at DESC
    LIMIT 1;

    -- Deactivate all others
    UPDATE workspace_members
    SET is_active = false
    WHERE user_id = user_record.user_id
    AND is_active = true
    AND id != first_membership_id;

    RAISE NOTICE 'Fixed multiple active workspaces for user %', user_record.user_id;
  END LOOP;
END $$;
