-- Fix workspace switching error P0001
-- This migration addresses the "User can only have one active workspace at a time" error

-- 1. Drop the existing trigger and function
DROP TRIGGER IF EXISTS ensure_single_active_workspace_trigger ON workspace_members;
DROP FUNCTION IF EXISTS ensure_single_active_workspace();

-- 2. <PERSON><PERSON> improved function without the problematic constraint check
CREATE OR REPLACE FUNCTION ensure_single_active_workspace()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting a workspace as active, deactivate all others for this user
  IF NEW.is_active = true AND (OLD.is_active IS NULL OR OLD.is_active = false) THEN
    -- Deactivate all other workspaces for this user in a single atomic operation
    UPDATE workspace_members 
    SET is_active = false, updated_at = NOW()
    WHERE user_id = NEW.user_id 
    AND id != NEW.id 
    AND is_active = true;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. Recreate the trigger
CREATE TRIGGER ensure_single_active_workspace_trigger
  BEFORE UPDATE ON workspace_members
  FOR EACH ROW
  EXECUTE FUNCTION ensure_single_active_workspace();

-- 4. Add a unique partial index to prevent multiple active workspaces at database level
-- This is a safer approach than throwing exceptions in triggers
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_workspace_members_user_active_unique
  ON workspace_members (user_id) 
  WHERE is_active = true;

-- 5. Create a function to safely switch workspace with proper transaction handling
CREATE OR REPLACE FUNCTION switch_user_workspace(
  p_user_id UUID,
  p_membership_id INTEGER
)
RETURNS TABLE (
  success BOOLEAN,
  message TEXT,
  workspace_data JSONB
) AS $$
DECLARE
  v_membership_record RECORD;
  v_workspace_data JSONB;
BEGIN
  -- Start transaction
  BEGIN
    -- Verify the membership exists and user has access
    SELECT wm.*, w.id as workspace_id, w.name as workspace_name, w.owner_id
    INTO v_membership_record
    FROM workspace_members wm
    JOIN workspaces w ON w.id = wm.workspace_id
    WHERE wm.id = p_membership_id 
    AND wm.user_id = p_user_id 
    AND wm.status = 'accepted'
    AND w.status = true;
    
    IF NOT FOUND THEN
      RETURN QUERY SELECT false, 'Invalid workspace membership'::TEXT, NULL::JSONB;
      RETURN;
    END IF;
    
    -- Deactivate all workspaces for this user first
    UPDATE workspace_members 
    SET is_active = false, updated_at = NOW()
    WHERE user_id = p_user_id AND is_active = true;
    
    -- Activate the target workspace
    UPDATE workspace_members 
    SET is_active = true, last_active = true, updated_at = NOW()
    WHERE id = p_membership_id;
    
    -- Prepare return data
    v_workspace_data := jsonb_build_object(
      'id', v_membership_record.workspace_id,
      'name', v_membership_record.workspace_name,
      'role', v_membership_record.role,
      'isOwner', v_membership_record.owner_id = p_user_id,
      'is_active', true
    );
    
    RETURN QUERY SELECT true, 'Workspace switched successfully'::TEXT, v_workspace_data;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error and return failure
      RAISE WARNING 'Error switching workspace: %', SQLERRM;
      RETURN QUERY SELECT false, 'Failed to switch workspace: ' || SQLERRM, NULL::JSONB;
  END;
END;
$$ LANGUAGE plpgsql;

-- 6. Create a function to get user's active workspace safely
CREATE OR REPLACE FUNCTION get_user_active_workspace(p_user_id UUID)
RETURNS TABLE (
  workspace_id INTEGER,
  workspace_name TEXT,
  role TEXT,
  is_owner BOOLEAN,
  membership_id INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    wm.role,
    (w.owner_id = p_user_id) as is_owner,
    wm.id as membership_id
  FROM workspace_members wm
  JOIN workspaces w ON w.id = wm.workspace_id
  WHERE wm.user_id = p_user_id 
  AND wm.is_active = true 
  AND wm.status = 'accepted'
  AND w.status = true
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 7. Clean up any existing data inconsistencies
DO $$
DECLARE
  user_record RECORD;
  active_count INTEGER;
  first_membership_id INTEGER;
BEGIN
  -- Find users with multiple active workspaces and fix them
  FOR user_record IN 
    SELECT user_id, COUNT(*) as active_count
    FROM workspace_members 
    WHERE is_active = true 
    GROUP BY user_id 
    HAVING COUNT(*) > 1
  LOOP
    -- Get the most recently updated membership for this user
    SELECT id INTO first_membership_id
    FROM workspace_members 
    WHERE user_id = user_record.user_id 
    AND is_active = true
    ORDER BY updated_at DESC, created_at DESC
    LIMIT 1;
    
    -- Deactivate all others
    UPDATE workspace_members 
    SET is_active = false 
    WHERE user_id = user_record.user_id 
    AND is_active = true 
    AND id != first_membership_id;
    
    RAISE NOTICE 'Fixed multiple active workspaces for user %', user_record.user_id;
  END LOOP;
END $$;
