import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../utils/auth";
import { createAuditLog } from "../../members/utils/audit";

/**
 * Switch active workspace for user
 * @route POST /api/workspace/switch
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    // Get workspace ID from request body
    const { workspaceId } = await request.json();

    if (!workspaceId) {
      return NextResponse.json({
        error: "Workspace ID is required"
      }, { status: 400 });
    }

    // Verify user is a member of the target workspace
    const { data: membership, error: membershipError } = await supabase
      .from("workspace_members")
      .select(`
        id,
        role,
        status,
        workspaces (
          id,
          name,
          status,
          owner_id
        )
      `)
      .eq("workspace_id", workspaceId)
      .eq("user_id", user.id)
      .eq("status", "accepted")
      .single();

    if (membershipError || !membership) {
      return NextResponse.json({
        error: "You are not a member of this workspace"
      }, { status: 403 });
    }

    // Check if workspace is active (status is a boolean field)
    if (membership.workspaces?.status !== true) {
      return NextResponse.json({
        error: "Workspace is not active"
      }, { status: 400 });
    }

    // First deactivate all workspaces for this user to prevent conflicts
    const { error: deactivateError } = await supabase
      .from("workspace_members")
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq("user_id", user.id);

    if (deactivateError) {
      console.error("Error deactivating workspaces:", deactivateError);
      return NextResponse.json({
        error: "Failed to switch workspace"
      }, { status: 500 });
    }

    // Then activate the target workspace
    const { error: updateError } = await supabase
      .from("workspace_members")
      .update({
        is_active: true,
        last_active: true,
        updated_at: new Date().toISOString()
      })
      .eq("id", membership.id);

    if (updateError) {
      console.error("Error switching workspace:", updateError);

      // Handle specific P0001 error
      if (updateError.code === 'P0001') {
        return NextResponse.json({
          error: "Cannot switch workspace: User can only have one active workspace at a time. Please try again."
        }, { status: 409 });
      }

      return NextResponse.json({
        error: "Failed to switch workspace"
      }, { status: 500 });
    }

    // Create audit log
    await createAuditLog({
      workspaceId: parseInt(workspaceId),
      userId: user.id,
      action: 'reactivated',
      performedBy: user.id,
      metadata: {
        switchedAt: new Date().toISOString(),
        previousWorkspace: null // Could track this if needed
      }
    });

    // Return the workspace data
    return NextResponse.json({
      message: "Workspace switched successfully",
      data: {
        id: membership.workspaces?.id,
        name: membership.workspaces?.name,
        role: membership.role,
        isOwner: membership.workspaces?.owner_id === user.id,
        is_active: true
      }
    }, { status: 200 });

  } catch (error) {
    console.error("Error switching workspace:", error);
    return NextResponse.json({
      error: "Internal server error"
    }, { status: 500 });
  }
}

/**
 * Get user's workspace memberships with optimized query
 * @route GET /api/workspace/switch
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    // Get all user's workspace memberships in a single optimized query
    const { data: memberships, error } = await supabase
      .from("workspace_members")
      .select(`
        id,
        role,
        is_active,
        last_active,
        workspaces (
          id,
          name,
          status,
          owner_id,
          company_type,
          created_at
        )
      `)
      .eq("user_id", user.id)
      .eq("status", "accepted")
      .eq("workspaces.status", true)
      .order("last_active", { ascending: false })
      .order("workspaces.created_at", { ascending: false });

    if (error) {
      console.error("Error fetching workspace memberships:", error);
      return NextResponse.json({
        error: "Failed to fetch workspaces"
      }, { status: 500 });
    }

    // Transform data for frontend
    const workspaces = memberships?.map(membership => ({
      id: membership.workspaces?.id,
      name: membership.workspaces?.name,
      role: membership.role,
      isActive: membership.is_active,
      isOwner: membership.workspaces?.owner_id === user.id,
      companyType: membership.workspaces?.company_type,
      membershipId: membership.id
    })) || [];

    // Find active workspace
    const activeWorkspace = workspaces.find(w => w.isActive);

    return NextResponse.json({
      data: {
        workspaces,
        activeWorkspace,
        totalCount: workspaces.length
      }
    }, { status: 200 });

  } catch (error) {
    console.error("Error fetching workspace memberships:", error);
    return NextResponse.json({
      error: "Internal server error"
    }, { status: 500 });
  }
}
